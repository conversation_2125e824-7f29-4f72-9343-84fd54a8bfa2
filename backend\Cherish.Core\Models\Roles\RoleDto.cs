namespace Cherish.Core.Models.Roles;

public class RoleDto
{
    public string Id { get; set; } = default!;
    public string Name { get; set; } = default!;
    public string? Description { get; set; }
    public string? TenantId { get; set; } = default!;
    public string? TenantName { get; set; }
    public List<string> Permissions { get; set; } = new List<string>();
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}
