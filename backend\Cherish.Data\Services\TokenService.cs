using Cherish.Core.Entities;
using Cherish.Core.Interfaces;
using Cherish.Core.Models.Auth;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;

namespace Cherish.Data.Services;

public class TokenService : ITokenService
{
    private readonly IConfiguration _configuration;
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly RoleManager<ApplicationRole> _roleManager;
    private readonly ApplicationDbContext _dbContext;

    public TokenService(
        IConfiguration configuration,
        UserManager<ApplicationUser> userManager,
        RoleManager<ApplicationRole> roleManager,
        ApplicationDbContext dbContext)
    {
        _configuration = configuration;
        _userManager = userManager;
        _roleManager = roleManager;
        _dbContext = dbContext;
    }

    public async Task<AuthResult> GenerateTokensAsync(object userObj)
    {
        if (userObj is not ApplicationUser user)
        {
            throw new ArgumentException("User object must be of type ApplicationUser", nameof(userObj));
        }

        var roles = await _userManager.GetRolesAsync(user);
        var claims = new List<Claim>
        {
            new Claim(JwtRegisteredClaimNames.Sub, user.Id),
            new Claim(JwtRegisteredClaimNames.Email, user.Email!),
            new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
            new Claim("firstName", user.FirstName),
            new Claim("lastName", user.LastName),
            new Claim("fullName", user.FullName)
        };

        // Add tenant claim if user belongs to a tenant
        if (user.TenantId.HasValue)
        {
            claims.Add(new Claim("tenantId", user.TenantId.Value.ToString()));
        }

        // Add roles as claims
        foreach (var role in roles)
        {
            claims.Add(new Claim(ClaimTypes.Role, role));

            // Get role permissions and add them as claims
            var roleObj = await _roleManager.FindByNameAsync(role);
            if (roleObj != null)
            {
                var permissions = await _roleManager.GetClaimsAsync(roleObj);
                foreach (var permission in permissions)
                {
                    claims.Add(new Claim("permission", permission.Value));
                }
            }
        }

        var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_configuration["Jwt:Key"]!));
        var creds = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);
        var expires = DateTime.UtcNow.AddMinutes(Convert.ToDouble(_configuration["Jwt:ExpiryInMinutes"]));

        var token = new JwtSecurityToken(
            issuer: _configuration["Jwt:Issuer"],
            audience: _configuration["Jwt:Audience"],
            claims: claims,
            expires: expires,
            signingCredentials: creds
        );

        var refreshToken = GenerateRefreshToken();

        // Save refresh token to database
        var refreshTokenEntity = new RefreshToken
        {
            Token = refreshToken,
            UserId = user.Id,
            ExpiryDate = DateTime.UtcNow.AddDays(Convert.ToDouble(_configuration["Jwt:RefreshTokenExpiryInDays"])),
            CreatedAt = DateTime.UtcNow,
            IsRevoked = false
        };

        // Remove old refresh tokens for this user
        var oldTokens = _dbContext.RefreshTokens.Where(rt => rt.UserId == user.Id);
        _dbContext.RefreshTokens.RemoveRange(oldTokens);

        await _dbContext.RefreshTokens.AddAsync(refreshTokenEntity);
        await _dbContext.SaveChangesAsync();

        return new AuthResult
        {
            Succeeded = true,
            AccessToken = new JwtSecurityTokenHandler().WriteToken(token),
            RefreshToken = refreshToken,
            ExpiresAt = expires,
            UserId = user.Id,
            Roles = roles.ToList()
        };
    }

    public async Task<AuthResult> RefreshTokenAsync(string refreshToken)
    {
        var storedToken = _dbContext.RefreshTokens.FirstOrDefault(rt => rt.Token == refreshToken);

        if (storedToken == null)
        {
            return new AuthResult
            {
                Succeeded = false,
                Errors = new List<string> { "Invalid refresh token" }
            };
        }

        if (storedToken.ExpiryDate < DateTime.UtcNow || storedToken.IsRevoked)
        {
            return new AuthResult
            {
                Succeeded = false,
                Errors = new List<string> { "Refresh token has expired or been revoked" }
            };
        }

        var user = await _userManager.FindByIdAsync(storedToken.UserId);

        if (user == null || !user.IsActive || user.IsDeleted)
        {
            return new AuthResult
            {
                Succeeded = false,
                Errors = new List<string> { "User not found or inactive" }
            };
        }

        // Remove the used refresh token
        _dbContext.RefreshTokens.Remove(storedToken);
        await _dbContext.SaveChangesAsync();

        // Generate new tokens
        return await GenerateTokensAsync(user);
    }

    public async Task<bool> RevokeTokenAsync(string userId)
    {
        var tokens = _dbContext.RefreshTokens.Where(rt => rt.UserId == userId);

        if (!tokens.Any())
        {
            return false;
        }

        _dbContext.RefreshTokens.RemoveRange(tokens);
        await _dbContext.SaveChangesAsync();

        return true;
    }

    private string GenerateRefreshToken()
    {
        var randomNumber = new byte[64];
        using var rng = RandomNumberGenerator.Create();
        rng.GetBytes(randomNumber);
        return Convert.ToBase64String(randomNumber);
    }
}
