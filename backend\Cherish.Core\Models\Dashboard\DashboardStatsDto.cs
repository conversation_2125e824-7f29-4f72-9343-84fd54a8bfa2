namespace Cherish.Core.Models.Dashboard;

public class DashboardStatsDto
{
    public int TotalLeads { get; set; }
    public int NewLeadsThisWeek { get; set; }
    public double ConversionRate { get; set; }
    public int PendingTasks { get; set; }
}

public class LeadsByStatusDto
{
    public string Status { get; set; } = null!;
    public int Count { get; set; }
}

public class LeadsBySourceDto
{
    public string Source { get; set; } = null!;
    public int Count { get; set; }
}

public class RecentLeadDto
{
    public Guid Id { get; set; }
    public string FullName { get; set; } = null!;
    public string? Company { get; set; }
    public string Status { get; set; } = null!;
    public DateTime CreatedAt { get; set; }
}
