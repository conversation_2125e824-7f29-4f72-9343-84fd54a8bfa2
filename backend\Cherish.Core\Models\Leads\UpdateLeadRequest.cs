using Cherish.Core.Entities;
using System.ComponentModel.DataAnnotations;

namespace Cherish.Core.Models.Leads;

public class UpdateLeadRequest
{
    [Required]
    [StringLength(100)]
    public string FirstName { get; set; } = null!;

    [Required]
    [StringLength(100)]
    public string LastName { get; set; } = null!;

    [Required]
    [EmailAddress]
    [StringLength(255)]
    public string Email { get; set; } = null!;

    [Phone]
    [StringLength(20)]
    public string? PhoneNumber { get; set; }

    [StringLength(100)]
    public string? Company { get; set; }

    [StringLength(100)]
    public string? JobTitle { get; set; }

    [StringLength(100)]
    public string? Source { get; set; }

    public LeadStatus Status { get; set; }

    public int? Score { get; set; }

    [StringLength(1000)]
    public string? Notes { get; set; }

    public Guid? AssignedToId { get; set; }
}
