namespace Cherish.Core.Entities;

public class LeadNote : BaseEntity
{
    public string Content { get; set; } = null!;

    // Related Lead
    public Guid LeadId { get; set; }
    public Lead Lead { get; set; } = null!;

    // Multi-tenant relationship
    public Guid TenantId { get; set; }
    public Tenant? Tenant { get; set; }

    // User who created the note
    public string CreatedById { get; set; } = default!;
    public ApplicationUser? CreatedBy { get; set; }
}
