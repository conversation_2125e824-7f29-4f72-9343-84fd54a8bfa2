using System;

namespace Cherish.Core.Entities;

public class UserPoints : BaseEntity
{
    public int AvailablePoints { get; set; }
    public int TotalEarnedPoints { get; set; }
    public int TotalRedeemedPoints { get; set; }
    
    // User who owns these points
    public string UserId { get; set; } = default!;
    public ApplicationUser? User { get; set; }
    
    // Multi-tenant relationship
    public Guid TenantId { get; set; }
    public Tenant? Tenant { get; set; }
}
