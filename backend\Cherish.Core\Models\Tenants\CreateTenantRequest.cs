using System.ComponentModel.DataAnnotations;

namespace Cherish.Core.Models.Tenants;

public class CreateTenantRequest
{
    [Required]
    public string Name { get; set; } = null!;

    public string? LogoUrl { get; set; }

    // Admin user information
    [Required]
    public string AdminFirstName { get; set; } = null!;

    [Required]
    public string AdminLastName { get; set; } = null!;

    [Required]
    [EmailAddress]
    public string AdminEmail { get; set; } = null!;

    [Required]
    [MinLength(8)]
    public string AdminPassword { get; set; } = null!;
}
