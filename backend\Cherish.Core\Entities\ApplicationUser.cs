using Microsoft.AspNetCore.Identity;
using System.Collections.Generic;

namespace Cherish.Core.Entities;

public class ApplicationUser : IdentityUser
{
    public string FirstName { get; set; } = null!;
    public string LastName { get; set; } = null!;
    public string? ProfilePictureUrl { get; set; }
    public bool IsActive { get; set; } = true;
    public bool IsDeleted { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }

    // Multi-tenant relationship
    public virtual Tenant? Tenant { get; set; }
    public Guid? TenantId { get; set; }

    // Reward and Recognition properties
    public virtual UserPoints? UserPoints { get; set; }
    public virtual ICollection<RewardTransaction> GivenRewards { get; set; } = new List<RewardTransaction>();
    public virtual ICollection<RewardTransaction> ReceivedRewards { get; set; } = new List<RewardTransaction>();
    public virtual ICollection<RewardRedemption> Redemptions { get; set; } = new List<RewardRedemption>();

    // Helper properties
    public string FullName => $"{FirstName} {LastName}";
}
