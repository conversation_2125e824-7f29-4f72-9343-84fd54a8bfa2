namespace Cherish.Core.Entities;

public class ContactNote : BaseEntity
{
    public string Content { get; set; } = null!;

    // Related Contact
    public Guid ContactId { get; set; }
    public Contact Contact { get; set; } = null!;

    // Multi-tenant relationship
    public Guid TenantId { get; set; }
    public Tenant? Tenant { get; set; }

    // User who created the note
    public string CreatedById { get; set; } = default!;
    public ApplicationUser? CreatedBy { get; set; }
}
