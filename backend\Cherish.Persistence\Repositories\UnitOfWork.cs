using Cherish.Core.Entities;
using Cherish.Core.Interfaces;
using Cherish.Data;

namespace Cherish.Persistence.Repositories;

public class UnitOfWork : IUnitOfWork
{
    private readonly ApplicationDbContext _dbContext;
    private bool _disposed;

    private IRepository<Tenant>? _tenants;
    private IRepository<Permission>? _permissions;

    public UnitOfWork(ApplicationDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public IRepository<Tenant> Tenants => _tenants ??= new Repository<Tenant>(_dbContext);
    public IRepository<Permission> Permissions => _permissions ??= new Repository<Permission>(_dbContext);

    public async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        return await _dbContext.SaveChangesAsync(cancellationToken);
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed && disposing)
        {
            _dbContext.Dispose();
        }
        _disposed = true;
    }
}
