using Cherish.Core.Entities;

namespace Cherish.Core.Constants;

public static class DefaultPermissions
{
    public static IEnumerable<Permission> GetAllPermissionObjects()
    {
        var permissions = new List<Permission>();
        var categories = new[] { "Users", "Roles", "Permissions", "Tenants", "Reports", "Rewards", "Transactions", "Redemptions" };
        var actions = new[] { "View", "Create", "Edit", "Delete", "Export", "Import", "Assign" };

        foreach (var category in categories)
        {
            foreach (var action in actions)
            {
                // Skip irrelevant combinations
                if ((category == "Permissions" && action == "Import") ||
                    (category == "Tenants" && action == "Import") ||
                    (category == "Reports" && action == "Delete") ||
                    (category == "Reports" && action == "Create") ||
                    (category == "Reports" && action == "Edit") ||
                    (category == "Rewards" && action == "Import") ||
                    (category == "Transactions" && action == "Import") ||
                    (category == "Transactions" && action == "Export") ||
                    (category == "Redemptions" && action == "Import") ||
                    (category == "Redemptions" && action == "Export"))
                {
                    continue;
                }

                var permissionName = $"{action}_{category}";
                permissions.Add(new Permission
                {
                    Id = Guid.NewGuid(),
                    Name = permissionName,
                    NormalizedName = permissionName.ToUpper(),
                    Description = $"Permission to {action.ToLower()} {category.ToLower()}",
                    Category = category,
                    CreatedAt = DateTime.UtcNow
                });
            }
        }

        return permissions;
    }

    // Keep the existing string-based permission methods if needed
    public static IEnumerable<string> GetAllPermissions() =>
        GetAllPermissionObjects().Select(p => p.ClaimValue);

    // Admin permissions - Complete access to all platform features
    public static IEnumerable<string> AdminPermissions =>
        GetAllPermissionObjects()
            .Where(p => p.Category is "Users" or "Roles" or "Permissions" or "Reports" or "Rewards" or "Transactions" or "Redemptions")
            .Select(p => p.ClaimValue);

    // RewardAdmin permissions - Can manage rewards catalog and redemptions
    public static IEnumerable<string> RewardAdminPermissions =>
        GetAllPermissionObjects()
            .Where(p =>
                (p.Category is "Rewards") ||
                (p.Category is "Transactions" && p.Name.StartsWith("View_")) ||
                (p.Category is "Redemptions"))
            .Select(p => p.ClaimValue);

    // User permissions - can give rewards to others in same tenant, view/redeem own rewards
    public static IEnumerable<string> UserPermissions =>
        GetAllPermissionObjects()
            .Where(p =>
                (p.Category is "Rewards" && p.Name.StartsWith("View_")) ||
                (p.Category is "Transactions" && (p.Name.StartsWith("View_") || p.Name.StartsWith("Create_"))) ||
                (p.Category is "Redemptions" && (p.Name.StartsWith("View_") || p.Name.StartsWith("Create_"))))
            .Select(p => p.ClaimValue);

    // Employee permissions - Basic user who can send/receive recognition and redeem own points
    public static IEnumerable<string> EmployeePermissions =>
        GetAllPermissionObjects()
            .Where(p =>
                (p.Category is "Rewards" && p.Name.StartsWith("View_")) ||
                (p.Category is "Transactions" && (p.Name.StartsWith("View_") || p.Name.StartsWith("Create_"))) ||
                (p.Category is "Redemptions" && (p.Name.StartsWith("View_") || p.Name.StartsWith("Create_"))))
            .Select(p => p.ClaimValue);
}