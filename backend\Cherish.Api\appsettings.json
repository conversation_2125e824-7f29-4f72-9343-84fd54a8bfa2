{"Database": {"Provider": "postgresql"}, "ConnectionStrings": {"DefaultConnection": "Host=postgres;Database=cherish;Username=cherish_user;Password=**********"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "Jwt": {"Key": "YourSuperSecretKeyHereMakeItLongAndComplex", "Issuer": "Cherish", "Audience": "CherishClients", "ExpiryInMinutes": 60, "RefreshTokenExpiryInDays": 7}, "Authentication": {"Google": {"ClientId": "google oauth client id", "ClientSecret": "google oauth client secret"}}}