using Google.Apis.Auth;
using Cherish.Core.Entities;
using Cherish.Core.Interfaces;
using Cherish.Core.Models.Auth;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Web;

namespace Cherish.Data.Services;

public class AuthService : IAuthService
{
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly RoleManager<ApplicationRole> _roleManager;
    private readonly ITokenService _tokenService;
    private readonly IConfiguration _configuration;
    private readonly Data.ApplicationDbContext _dbContext;
    private readonly ITenantService _tenantService;

    public AuthService(
        UserManager<ApplicationUser> userManager,
        RoleManager<ApplicationRole> roleManager,
        ITokenService tokenService,
        IConfiguration configuration,
        Data.ApplicationDbContext dbContext,
        ITenantService tenantService)
    {
        _userManager = userManager;
        _roleManager = roleManager;
        _tokenService = tokenService;
        _configuration = configuration;
        _dbContext = dbContext;
        _tenantService = tenantService;
    }

    public async Task<AuthResult> RegisterAsync(RegisterRequest request, CancellationToken cancellationToken = default)
    {
        // Check if user already exists
        var existingUser = await _userManager.FindByEmailAsync(request.Email);
        if (existingUser != null)
        {
            return new AuthResult
            {
                Succeeded = false,
                Errors = new List<string> { "User with this email already exists" }
            };
        }

        // Create new user
        var user = new ApplicationUser
        {
            UserName = request.Email,
            Email = request.Email,
            FirstName = request.FirstName,
            LastName = request.LastName,
            CreatedAt = DateTime.UtcNow,
            IsActive = false, // Set to false until email is verified
            EmailConfirmed = false // Email not confirmed yet
        };

        // If tenant name is provided, find the tenant and associate user with it
        if (!string.IsNullOrEmpty(request.TenantName))
        {
            var tenant = await _tenantService.GetByNameAsync(request.TenantName);

            if (tenant == null || !tenant.IsActive || tenant.IsDeleted)
            {
                return new AuthResult
                {
                    Succeeded = false,
                    Errors = new List<string> { "Tenant not found or inactive" }
                };
            }

            user.TenantId = tenant.Id;
        }
        else
        {
            // If no tenant is specified, assign the default tenant
            var defaultTenant = await _tenantService.GetByNameAsync("Default", cancellationToken);

            if (defaultTenant != null)
            {
                user.TenantId = defaultTenant.Id;
            }
        }

        // Create user in identity system
        var result = await _userManager.CreateAsync(user, request.Password);

        if (!result.Succeeded)
        {
            return new AuthResult
            {
                Succeeded = false,
                Errors = result.Errors.Select(e => e.Description).ToList()
            };
        }

        // Assign default role
        await _userManager.AddToRoleAsync(user, "User");

        // Generate email verification token
        var token = await _userManager.GenerateEmailConfirmationTokenAsync(user);

        // Encode the token for URL safety
        var encodedToken = HttpUtility.UrlEncode(token);

        // Create verification link
        var frontendBaseUrl = _configuration["App:FrontendBaseUrl"];
        var verificationLink = $"{frontendBaseUrl}/verify-email?userId={user.Id}&token={encodedToken}";

        // Return a success message without tokens since the user needs to verify email first
        return new AuthResult
        {
            Succeeded = true
        };
    }

    public async Task<AuthResult> LoginAsync(LoginRequest request, CancellationToken cancellationToken = default)
    {
        // Find user by email
        var user = await _userManager.FindByEmailAsync(request.Email);

        if (user == null)
        {
            return new AuthResult
            {
                Succeeded = false,
                Errors = new List<string> { "Invalid email or password" }
            };
        }

        // Check if user is active
        if (!user.IsActive || user.IsDeleted)
        {
            return new AuthResult
            {
                Succeeded = false,
                Errors = new List<string> { "User account is inactive or deleted" }
            };
        }

        // Check if email is confirmed
        if (!user.EmailConfirmed)
        {
            return new AuthResult
            {
                Succeeded = false,
                Errors = new List<string> { "Please verify your email address before logging in" }
            };
        }

        // Verify password
        var isPasswordValid = await _userManager.CheckPasswordAsync(user, request.Password);

        if (!isPasswordValid)
        {
            return new AuthResult
            {
                Succeeded = false,
                Errors = new List<string> { "Invalid email or password" }
            };
        }

        // Generate tokens
        return await _tokenService.GenerateTokensAsync(user);
    }

    public async Task<AuthResult> GoogleLoginAsync(GoogleAuthRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            GoogleJsonWebSignature.Payload payload;

            // Check if we have an ID token or an authorization code
            if (!string.IsNullOrEmpty(request.IdToken))
            {
                // Validate Google ID token
                var settings = new GoogleJsonWebSignature.ValidationSettings
                {
                    Audience = new List<string> { _configuration["Authentication:Google:ClientId"]! }
                };

                payload = await GoogleJsonWebSignature.ValidateAsync(request.IdToken, settings);
            }
            else if (!string.IsNullOrEmpty(request.Code))
            {
                // Exchange authorization code for tokens
                using var httpClient = new HttpClient();
                var tokenRequest = new Dictionary<string, string>
                {
                    ["code"] = request.Code,
                    ["client_id"] = _configuration["Authentication:Google:ClientId"]!,
                    ["client_secret"] = _configuration["Authentication:Google:ClientSecret"]!,
                    ["redirect_uri"] = request.RedirectUri ?? "",
                    ["grant_type"] = "authorization_code"
                };

                var content = new FormUrlEncodedContent(tokenRequest);
                var response = await httpClient.PostAsync("https://oauth2.googleapis.com/token", content, cancellationToken);

                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync(cancellationToken);
                    throw new Exception($"Failed to exchange authorization code: {errorContent}");
                }

                var responseStream = await response.Content.ReadAsStreamAsync(cancellationToken);
                var tokenResponse = await JsonSerializer.DeserializeAsync<Dictionary<string, JsonElement>>(responseStream, cancellationToken: cancellationToken);
                var idToken = tokenResponse?["id_token"].GetString();

                if (string.IsNullOrEmpty(idToken))
                {
                    throw new Exception("ID token not found in token response");
                }

                // Validate the ID token
                var settings = new GoogleJsonWebSignature.ValidationSettings
                {
                    Audience = new List<string> { _configuration["Authentication:Google:ClientId"]! }
                };

                payload = await GoogleJsonWebSignature.ValidateAsync(idToken, settings);
            }
            else
            {
                throw new ArgumentException("Either IdToken or Code must be provided");
            }

            // Check if user exists
            var user = await _userManager.FindByEmailAsync(payload.Email);

            if (user == null)
            {
                // Create new user
                user = new ApplicationUser
                {
                    UserName = payload.Email,
                    Email = payload.Email,
                    FirstName = payload.GivenName ?? "Google",
                    LastName = payload.FamilyName ?? "User",
                    ProfilePictureUrl = payload.Picture,
                    EmailConfirmed = true,
                    CreatedAt = DateTime.UtcNow,
                    IsActive = true
                };

                // Create user without password
                var result = await _userManager.CreateAsync(user);

                if (!result.Succeeded)
                {
                    return new AuthResult
                    {
                        Succeeded = false,
                        Errors = result.Errors.Select(e => e.Description).ToList()
                    };
                }

                // Add external login info
            var info = new UserLoginInfo("Google", payload.Subject, "Google");
            result = await _userManager.AddLoginAsync(user, info);

            if (!result.Succeeded)
            {
                return new AuthResult
                {
                    Succeeded = false,
                    Errors = result.Errors.Select(e => e.Description).ToList()
                };
            }

                // Assign default role
                await _userManager.AddToRoleAsync(user, "User");
            }
            else
            {
                // Check if user is active
                if (!user.IsActive || user.IsDeleted)
                {
                    return new AuthResult
                    {
                        Succeeded = false,
                        Errors = new List<string> { "User account is inactive or deleted" }
                    };
                }

                // Check if the external login already exists
            var existingLogins = await _userManager.GetLoginsAsync(user);
            var googleLogin = existingLogins.FirstOrDefault(l => l.LoginProvider == "Google");

            if (googleLogin == null)
            {
                // Add external login info if it doesn't exist
                var info = new UserLoginInfo("Google", payload.Subject, "Google");
                var result = await _userManager.AddLoginAsync(user, info);

                if (!result.Succeeded)
                {
                    return new AuthResult
                    {
                        Succeeded = false,
                        Errors = result.Errors.Select(e => e.Description).ToList()
                    };
                }
            }

                // If tenant name is provided, verify user belongs to that tenant
                if (!string.IsNullOrEmpty(request.TenantName))
                {
                    var tenant = await _dbContext.Set<Tenant>()
                        .FirstOrDefaultAsync(t => t.Name == request.TenantName && t.IsActive && !t.IsDeleted, cancellationToken);

                    if (tenant == null)
                    {
                        return new AuthResult
                        {
                            Succeeded = false,
                            Errors = new List<string> { "Tenant not found or inactive" }
                        };
                    }

                    if (user.TenantId != tenant.Id)
                    {
                        return new AuthResult
                        {
                            Succeeded = false,
                            Errors = new List<string> { "User does not belong to this tenant" }
                        };
                    }
                }

                // Update user profile picture if changed
                if (user.ProfilePictureUrl != payload.Picture)
                {
                    user.ProfilePictureUrl = payload.Picture;
                    await _userManager.UpdateAsync(user);
                }
            }

            // Generate tokens
            return await _tokenService.GenerateTokensAsync(user);
        }
        catch (Exception ex)
        {
            return new AuthResult
            {
                Succeeded = false,
                Errors = new List<string> { $"Google authentication failed: {ex.Message}" }
            };
        }
    }

    public async Task<AuthResult> RefreshTokenAsync(RefreshTokenRequest request, CancellationToken cancellationToken = default)
    {
        return await _tokenService.RefreshTokenAsync(request.RefreshToken);
    }

    public async Task<bool> RevokeTokenAsync(string userId, CancellationToken cancellationToken = default)
    {
        return await _tokenService.RevokeTokenAsync(userId);
    }

    protected virtual async Task<Tenant?> GetDefaultTenantAsync(CancellationToken cancellationToken = default)
    {
        return await _dbContext.Set<Tenant>()
            .FirstOrDefaultAsync(t => t.Name == "Default" && t.IsActive && !t.IsDeleted, cancellationToken);
    }
}
