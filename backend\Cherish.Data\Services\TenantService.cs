using Cherish.Core.Entities;
using Cherish.Core.Interfaces;
using Cherish.Core.Models.Tenants;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;

namespace Cherish.Data.Services;

public class TenantService : ITenantService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly RoleManager<ApplicationRole> _roleManager;

    public TenantService(
        IUnitOfWork unitOfWork,
        UserManager<ApplicationUser> userManager,
        RoleManager<ApplicationRole> roleManager)
    {
        _unitOfWork = unitOfWork;
        _userManager = userManager;
        _roleManager = roleManager;
    }

    public async Task<TenantDto> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var tenant = await _unitOfWork.Tenants.GetByIdAsync(id, cancellationToken);

        if (tenant == null)
        {
            throw new KeyNotFoundException($"Tenant with ID {id} not found");
        }

        return MapToDtoAsync(tenant, cancellationToken);
    }

    public async Task<TenantDto?> GetByNameAsync(string name, CancellationToken cancellationToken = default)
    {
        var tenants = await _unitOfWork.Tenants.FindAsync(t => t.Name == name, cancellationToken);
        var tenant = tenants.FirstOrDefault();

        if (tenant == null)
        {
            return null;
        }

        return MapToDtoAsync(tenant, cancellationToken);
    }

    public async Task<IEnumerable<TenantDto>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        var tenants = await _unitOfWork.Tenants.GetAllAsync(cancellationToken);
        return tenants.Select(t => MapToDtoAsync(t, cancellationToken));
    }

    public async Task<TenantDto> CreateAsync(CreateTenantRequest request, CancellationToken cancellationToken = default)
    {
        // Check if name is already in use
        var existingTenants = await _unitOfWork.Tenants.FindAsync(t => t.Name == request.Name, cancellationToken);

        if (existingTenants.Any())
        {
            throw new InvalidOperationException($"Tenant name '{request.Name}' is already in use");
        }

        // Create tenant
        var tenant = new Tenant
        {
            Id = Guid.NewGuid(),
            Name = request.Name,
            LogoUrl = request.LogoUrl,
            IsActive = true,
            CreatedAt = DateTime.UtcNow
        };

        await _unitOfWork.Tenants.AddAsync(tenant, cancellationToken);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        return MapToDtoAsync(tenant, cancellationToken);
    }

    public async Task<TenantDto> UpdateAsync(Guid id, UpdateTenantRequest request, CancellationToken cancellationToken = default)
    {
        var tenant = await _unitOfWork.Tenants.GetByIdAsync(id, cancellationToken);

        if (tenant == null)
        {
            throw new KeyNotFoundException($"Tenant with ID {id} not found");
        }

        // Update properties if provided
        if (!string.IsNullOrEmpty(request.Name))
        {
            tenant.Name = request.Name;
        }

        if (request.LogoUrl != null)
        {
            tenant.LogoUrl = request.LogoUrl;
        }

        await _unitOfWork.Tenants.UpdateAsync(tenant, cancellationToken);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        return MapToDtoAsync(tenant, cancellationToken);
    }

    public async Task<bool> DeactivateAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var tenant = await _unitOfWork.Tenants.GetByIdAsync(id, cancellationToken);

        if (tenant == null)
        {
            return false;
        }

        tenant.IsActive = false;
        await _unitOfWork.Tenants.UpdateAsync(tenant, cancellationToken);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        return true;
    }

    public async Task<bool> ActivateAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var tenant = await _unitOfWork.Tenants.GetByIdAsync(id, cancellationToken);

        if (tenant == null)
        {
            return false;
        }

        tenant.IsActive = true;
        await _unitOfWork.Tenants.UpdateAsync(tenant, cancellationToken);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        return true;
    }

    public async Task<bool> DeleteAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var tenant = await _unitOfWork.Tenants.GetByIdAsync(id, cancellationToken);

        if (tenant == null)
        {
            return false;
        }

        // This is a soft delete
        await _unitOfWork.Tenants.DeleteAsync(tenant, cancellationToken);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        return true;
    }

    private TenantDto MapToDtoAsync(Tenant tenant, CancellationToken cancellationToken)
    {
        return new TenantDto
        {
            Id = tenant.Id,
            Name = tenant.Name,
            LogoUrl = tenant.LogoUrl,
            IsActive = tenant.IsActive,
            CreatedAt = tenant.CreatedAt,
            UpdatedAt = tenant.UpdatedAt
        };
    }
}
