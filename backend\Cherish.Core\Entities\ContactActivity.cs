namespace Cherish.Core.Entities;

public class ContactActivity : BaseEntity
{
    public string Title { get; set; } = null!;
    public string? Description { get; set; }
    public ContactActivityType Type { get; set; }
    public DateTime ActivityDate { get; set; }
    public bool IsCompleted { get; set; }

    // Related Contact
    public Guid ContactId { get; set; }
    public Contact Contact { get; set; } = null!;

    // Multi-tenant relationship
    public Guid TenantId { get; set; }
    public Tenant? Tenant { get; set; }

    // User who created/performed the activity
    public string CreatedById { get; set; } = default!;
    public ApplicationUser? CreatedBy { get; set; }
}

public enum ContactActivityType
{
    Call,
    Email,
    Meeting,
    Task,
    Note,
    Other
}
