using System.ComponentModel.DataAnnotations;

namespace Cherish.Core.Models.Auth;

public class GoogleAuthRequest
{
    /// <summary>
    /// The ID token from Google. This is used when the frontend already has the ID token.
    /// </summary>
    public string? IdToken { get; set; }

    /// <summary>
    /// The authorization code from Google. This is used when the frontend has the authorization code.
    /// </summary>
    public string? Code { get; set; }

    /// <summary>
    /// The redirect URI used in the authorization request. Required when using Code.
    /// </summary>
    public string? RedirectUri { get; set; }

    public string? TenantName { get; set; }
}
