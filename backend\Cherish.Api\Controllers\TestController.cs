using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Cherish.Api.Controllers;

/// <summary>
/// Test controller for verifying authentication and authorization
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class TestController : ControllerBase
{
    private readonly ILogger<TestController> _logger;

    /// <summary>
    /// Initializes a new instance of the <see cref="TestController"/> class.
    /// </summary>
    /// <param name="logger">The logger.</param>
    public TestController(ILogger<TestController> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// Gets a public message that doesn't require authentication.
    /// </summary>
    /// <returns>A public message.</returns>
    [HttpGet("public")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public IActionResult GetPublic()
    {
        return Ok(new { message = "This is a public endpoint" });
    }

    /// <summary>
    /// Gets a message that requires authentication.
    /// </summary>
    /// <returns>A message with the authenticated user's name.</returns>
    [HttpGet("authenticated")]
    [Authorize]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public IActionResult GetAuthenticated()
    {
        return Ok(new { message = "This is an authenticated endpoint", user = User.Identity?.Name });
    }

    /// <summary>
    /// Gets a message that requires admin role.
    /// </summary>
    /// <returns>A message with the admin user's name.</returns>
    [HttpGet("admin")]
    [Authorize(Roles = "Admin,SuperAdmin")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public IActionResult GetAdmin()
    {
        return Ok(new { message = "This is an admin endpoint", user = User.Identity?.Name });
    }
}
