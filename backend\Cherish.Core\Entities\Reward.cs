using System;
using System.Collections.Generic;

namespace Cherish.Core.Entities;

public class Reward : BaseEntity
{
    public string Title { get; set; } = null!;
    public string? Description { get; set; }
    public int PointsValue { get; set; }
    public RewardType Type { get; set; } = RewardType.Recognition;
    public RewardStatus Status { get; set; } = RewardStatus.Active;
    
    // Multi-tenant relationship
    public Guid TenantId { get; set; }
    public Tenant? Tenant { get; set; }
    
    // User who created the reward
    public string CreatedById { get; set; } = default!;
    public ApplicationUser? CreatedBy { get; set; }
    
    // Navigation properties
    public ICollection<RewardTransaction> Transactions { get; set; } = new List<RewardTransaction>();
}

public enum RewardType
{
    Recognition, // Non-monetary recognition
    Points,      // Points-based reward
    Badge,       // Achievement badge
    Certificate  // Certificate of achievement
}

public enum RewardStatus
{
    Active,
    Inactive,
    Archived
}
