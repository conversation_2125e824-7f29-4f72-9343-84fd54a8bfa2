namespace Cherish.Core.Entities;

public class LeadActivity : BaseEntity
{
    public string Title { get; set; } = null!;
    public string? Description { get; set; }
    public LeadActivityType Type { get; set; }
    public DateTime ActivityDate { get; set; }
    public bool IsCompleted { get; set; }

    // Related Lead
    public Guid LeadId { get; set; }
    public Lead Lead { get; set; } = null!;

    // Multi-tenant relationship
    public Guid TenantId { get; set; }
    public Tenant? Tenant { get; set; }

    // User who created/performed the activity
    public string CreatedById { get; set; } = default!;
    public ApplicationUser? CreatedBy { get; set; }
}

public enum LeadActivityType
{
    Call,
    Email,
    Meeting,
    Task,
    Note,
    StatusChange,
    Other
}
