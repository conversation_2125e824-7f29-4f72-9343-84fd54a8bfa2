
namespace Cherish.Core.Entities;

public class Tenant : BaseEntity
{
    public string Name { get; set; } = null!;
    public string Domain { get; set; } = null!;
    public string? LogoUrl { get; set; }
    public string? Description { get; set; }
    public bool IsActive { get; set; } = true;

    // Navigation property for users
    public virtual ICollection<ApplicationUser> Users { get; set; } = new List<ApplicationUser>();
}