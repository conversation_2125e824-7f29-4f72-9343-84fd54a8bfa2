using Cherish.Core.Models.Permissions;

namespace Cherish.Core.Interfaces;

public interface IPermissionService
{
    Task<PermissionDto> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<IEnumerable<PermissionDto>> GetAllAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<PermissionDto>> GetByCategoryAsync(string category, CancellationToken cancellationToken = default);
    Task<PermissionDto> CreateAsync(CreatePermissionRequest request, CancellationToken cancellationToken = default);
    Task<PermissionDto> UpdateAsync(Guid id, UpdatePermissionRequest request, CancellationToken cancellationToken = default);
    Task<bool> DeleteAsync(Guid id, CancellationToken cancellationToken = default);
    Task<IEnumerable<PermissionDto>> GetByRoleIdAsync(Guid roleId, CancellationToken cancellationToken = default);
}
