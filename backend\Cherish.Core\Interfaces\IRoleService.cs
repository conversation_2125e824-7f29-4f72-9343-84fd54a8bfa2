using Cherish.Core.Models.Roles;

namespace Cherish.Core.Interfaces;

public interface IRoleService
{
    Task<RoleDto> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<IEnumerable<RoleDto>> GetAllAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<RoleDto>> GetByTenantIdAsync(Guid tenantId, CancellationToken cancellationToken = default);
    Task<RoleDto> CreateAsync(CreateRoleRequest request, CancellationToken cancellationToken = default);
    Task<RoleDto> UpdateAsync(Guid id, UpdateRoleRequest request, CancellationToken cancellationToken = default);
    Task<bool> DeleteAsync(Guid id, CancellationToken cancellationToken = default);
    Task<bool> AssignToUserAsync(Guid roleId, Guid userId, CancellationToken cancellationToken = default);
    Task<bool> RemoveFromUserAsync(Guid roleId, Guid userId, CancellationToken cancellationToken = default);
    Task<bool> AssignPermissionAsync(Guid roleId, Guid permissionId, CancellationToken cancellationToken = default);
    Task<bool> RemovePermissionAsync(Guid roleId, Guid permissionId, CancellationToken cancellationToken = default);
}
