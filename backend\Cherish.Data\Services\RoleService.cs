using Cherish.Core.Entities;
using Cherish.Core.Interfaces;
using Cherish.Core.Models.Roles;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;

namespace Cherish.Data.Services;

public class RoleService : IRoleService
{
    private readonly RoleManager<ApplicationRole> _roleManager;

    public RoleService(RoleManager<ApplicationRole> roleManager)
    {
        _roleManager = roleManager;
    }

    public Task<bool> AssignPermissionAsync(Guid roleId, Guid permissionId, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }

    public Task<bool> AssignToUserAsync(Guid roleId, Guid userId, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }

    public async Task<RoleDto> CreateAsync(CreateRoleRequest request, CancellationToken cancellationToken = default)
    {
        var role = new ApplicationRole
        {
            Name = request.Name,
            NormalizedName = request.Name.ToUpper(),
            Description = request.Description,
            Id = Guid.NewGuid().ToString(),
            TenantId = request.TenantId
        };

        var result = await _roleManager.CreateAsync(role);
        if (!result.Succeeded)
        {
            throw new InvalidOperationException($"Failed to create role: {string.Join(", ", result.Errors.Select(e => e.Description))}");
        }

        foreach (var permission in request.Permissions)
        {
            var claimResult = await _roleManager.AddClaimAsync(role, new System.Security.Claims.Claim("permission", permission));
            if (!claimResult.Succeeded)
            {
                throw new InvalidOperationException($"Failed to add permission '{permission}' to role: {string.Join(", ", claimResult.Errors.Select(e => e.Description))}");
            }
        }

        return new RoleDto
        {
            Id = role.Id,
            Name = role.Name,
            Permissions = request.Permissions
        };
    }

    public Task<bool> DeleteAsync(Guid id, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }

    public Task<IEnumerable<RoleDto>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }

    public Task<RoleDto> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }

    public Task<IEnumerable<RoleDto>> GetByTenantIdAsync(Guid tenantId, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }

    public Task<bool> RemoveFromUserAsync(Guid roleId, Guid userId, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }

    public Task<bool> RemovePermissionAsync(Guid roleId, Guid permissionId, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }

    public Task<RoleDto> UpdateAsync(Guid id, UpdateRoleRequest request, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }

}