using Cherish.Core.Models.Dashboard;

namespace Cherish.Core.Interfaces;

public interface IDashboardService
{
    Task<DashboardStatsDto> GetDashboardStatsAsync(Guid tenantId, CancellationToken cancellationToken = default);
    Task<IEnumerable<LeadsByStatusDto>> GetLeadsByStatusAsync(Guid tenantId, CancellationToken cancellationToken = default);
    Task<IEnumerable<LeadsBySourceDto>> GetLeadsBySourceAsync(Guid tenantId, CancellationToken cancellationToken = default);
    Task<IEnumerable<RecentLeadDto>> GetRecentLeadsAsync(Guid tenantId, int count = 5, CancellationToken cancellationToken = default);
}
