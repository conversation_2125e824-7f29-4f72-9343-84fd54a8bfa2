using Cherish.Core.Entities;
using Cherish.Core.Models.Leads;

namespace Cherish.Core.Interfaces;

public interface ILeadService
{
    Task<LeadDto> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<IEnumerable<LeadDto>> GetAllAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<LeadDto>> GetByTenantIdAsync(Guid tenantId, CancellationToken cancellationToken = default);
    Task<IEnumerable<LeadDto>> GetByAssignedToIdAsync(Guid userId, CancellationToken cancellationToken = default);
    Task<IEnumerable<LeadDto>> GetByStatusAsync(LeadStatus status, Guid tenantId, CancellationToken cancellationToken = default);
    Task<LeadDto> CreateAsync(CreateLeadRequest request, Guid tenantId, CancellationToken cancellationToken = default);
    Task<LeadDto> UpdateAsync(Guid id, UpdateLeadRequest request, CancellationToken cancellationToken = default);
    Task DeleteAsync(Guid id, CancellationToken cancellationToken = default);
}
