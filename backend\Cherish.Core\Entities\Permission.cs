namespace Cherish.Core.Entities;

public class Permission : BaseEntity
{
    private string _category = null!;
    private string _normalizedName = null!;

    public string Name { get; set; } = null!;

    public string NormalizedName
    {
        get => _normalizedName;
        set
        {
            _normalizedName = value;
            UpdateClaimValue();
        }
    }

    public string? Description { get; set; }

    public string Category
    {
        get => _category;
        set
        {
            _category = value;
            UpdateClaimValue();
        }
    }

    public string ClaimValue { get; private set; } = string.Empty;

    private void UpdateClaimValue()
    {
        if (!string.IsNullOrEmpty(_category) && !string.IsNullOrEmpty(_normalizedName))
        {
            ClaimValue = $"{_category}.{_normalizedName}";
        }
    }
}