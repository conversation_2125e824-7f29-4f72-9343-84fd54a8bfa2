using System;

namespace Cherish.Core.Entities;

public class RewardRedemption : BaseEntity
{
    public int PointsRedeemed { get; set; }
    public RedemptionStatus Status { get; set; } = RedemptionStatus.Pending;
    public string? Notes { get; set; }
    
    // Related transaction
    public Guid TransactionId { get; set; }
    public RewardTransaction Transaction { get; set; } = null!;
    
    // Multi-tenant relationship
    public Guid TenantId { get; set; }
    public Tenant? Tenant { get; set; }
    
    // User who redeemed the reward
    public string RedeemedById { get; set; } = default!;
    public ApplicationUser? RedeemedBy { get; set; }
    
    // User who processed the redemption (optional)
    public string? ProcessedById { get; set; }
    public ApplicationUser? ProcessedBy { get; set; }
}

public enum RedemptionStatus
{
    Pending,
    Approved,
    Rejected,
    Fulfilled,
    Cancelled
}
