using Cherish.Core.Entities;
using Cherish.Core.Interfaces;
using Cherish.Core.Models.Permissions;
using Microsoft.AspNetCore.Identity;

namespace Cherish.Data.Services;

public class PermissionService : IPermissionService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly RoleManager<ApplicationRole> _roleManager;

    public PermissionService(IUnitOfWork unitOfWork, RoleManager<ApplicationRole> roleManager)
    {
        _unitOfWork = unitOfWork;
        _roleManager = roleManager;
    }

    public async Task<PermissionDto> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var permission = await _unitOfWork.Permissions.GetByIdAsync(id, cancellationToken);

        if (permission == null)
        {
            throw new KeyNotFoundException($"Permission with ID {id} not found");
        }

        return MapToDto(permission);
    }

    public async Task<IEnumerable<PermissionDto>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        var permissions = await _unitOfWork.Permissions.GetAllAsync(cancellationToken);
        return permissions.Select(MapToDto);
    }

    public async Task<IEnumerable<PermissionDto>> GetByCategoryAsync(string category, CancellationToken cancellationToken = default)
    {
        var permissions = await _unitOfWork.Permissions.FindAsync(p => p.Category == category, cancellationToken);
        return permissions.Select(MapToDto);
    }

    public async Task<PermissionDto> CreateAsync(CreatePermissionRequest request, CancellationToken cancellationToken = default)
    {
        // Check if permission with same name already exists
        var existingPermissions = await _unitOfWork.Permissions.FindAsync(
            p => p.NormalizedName == request.Name.ToUpper(),
            cancellationToken);

        if (existingPermissions.Any())
        {
            throw new InvalidOperationException($"Permission with name '{request.Name}' already exists");
        }

        var permission = new Permission
        {
            Id = Guid.NewGuid(),
            Name = request.Name,
            NormalizedName = request.Name.ToUpper(),
            Description = request.Description,
            Category = request.Category,
            CreatedAt = DateTime.UtcNow
        };

        await _unitOfWork.Permissions.AddAsync(permission, cancellationToken);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        return MapToDto(permission);
    }

    public async Task<PermissionDto> UpdateAsync(Guid id, UpdatePermissionRequest request, CancellationToken cancellationToken = default)
    {
        var permission = await _unitOfWork.Permissions.GetByIdAsync(id, cancellationToken);

        if (permission == null)
        {
            throw new KeyNotFoundException($"Permission with ID {id} not found");
        }

        // Update properties if provided
        if (!string.IsNullOrEmpty(request.Name))
        {
            // Check if permission with same name already exists
            var existingPermissions = await _unitOfWork.Permissions.FindAsync(
                p => p.NormalizedName == request.Name.ToUpper() && p.Id != id,
                cancellationToken);

            if (existingPermissions.Any())
            {
                throw new InvalidOperationException($"Permission with name '{request.Name}' already exists");
            }

            permission.Name = request.Name;
            permission.NormalizedName = request.Name.ToUpper();
        }

        if (request.Description != null)
        {
            permission.Description = request.Description;
        }

        if (!string.IsNullOrEmpty(request.Category))
        {
            permission.Category = request.Category;
        }

        await _unitOfWork.Permissions.UpdateAsync(permission, cancellationToken);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        return MapToDto(permission);
    }

    public async Task<bool> DeleteAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var permission = await _unitOfWork.Permissions.GetByIdAsync(id, cancellationToken);

        if (permission == null)
        {
            return false;
        }

        // Check if permission is in use
       var isInUse = await IsPermissionInUseAsync(permission);
        if (isInUse)
        {
            throw new InvalidOperationException("Cannot delete permission that is assigned to roles");
        }

        await _unitOfWork.Permissions.DeleteAsync(permission, cancellationToken);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        return true;
    }

    public async Task<IEnumerable<PermissionDto>> GetByRoleIdAsync(Guid roleId, CancellationToken cancellationToken = default)
    {
        var permissions = new List<Permission>();
        var role = await _roleManager.FindByIdAsync(roleId.ToString());
        if (role == null)
        {
            throw new KeyNotFoundException($"Role with ID {roleId} not found");
        }

        var rolePermissions = (await _roleManager.GetClaimsAsync(role)).Where(p => p.Type == "Permission");

        foreach (var rolePermission in rolePermissions)
        {
            var permission = await _unitOfWork.Permissions.FindAsync(p => p.ClaimValue == rolePermission.Value, cancellationToken);

            if (permission != null)
            {
                permissions.AddRange(permission);
            }
        }

        return permissions.Select(MapToDto);
    }

    private PermissionDto MapToDto(Permission permission)
    {
        return new PermissionDto
        {
            Id = permission.Id,
            Name = permission.Name,
            Description = permission.Description,
            Category = permission.Category,
            CreatedAt = permission.CreatedAt,
            UpdatedAt = permission.UpdatedAt
        };
    }

    private class PermissionEqualityComparer : IEqualityComparer<Permission>
    {
        public bool Equals(Permission? x, Permission? y)
        {
            if (x == null || y == null)
            {
                return x == y;
            }

            return x.Id == y.Id;
        }

        public int GetHashCode(Permission obj)
        {
            return obj.Id.GetHashCode();
        }
    }

    public async Task<bool> IsPermissionInUseAsync(Permission permission)
    {
        // Get all roles
        var roles = _roleManager.Roles.ToList();

        foreach (var role in roles)
        {
            // Get claims for the current role
            var claims = await _roleManager.GetClaimsAsync(role);

            // Check if any claim matches the permission
            if (claims.Any(c => c.Type == "Permission" && c.Value == permission.ClaimValue))
            {
                return true; // Permission is in use
            }
        }

        return false; // Permission is not in use
    }

}
