using System.Security.Claims;
using Microsoft.AspNetCore.Authorization;

namespace Cherish.Core.Authorization;

public class PermissionAuthorizationHandler : AuthorizationHandler<PermissionRequirement>
{
    protected override Task HandleRequirementAsync(AuthorizationHandlerContext context, PermissionRequirement requirement)
    {
        var permissionClaims = context.User.Claims
            .Where(x => x.Type == "permission")
            .Select(x => x.Value);

        if (permissionClaims.Contains(requirement.Permission))
        {
            context.Succeed(requirement);
        }

        return Task.CompletedTask;
    }
}