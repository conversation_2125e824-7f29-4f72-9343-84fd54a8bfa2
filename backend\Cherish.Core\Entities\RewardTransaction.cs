using System;

namespace Cherish.Core.Entities;

public class RewardTransaction : BaseEntity
{
    public string? Comment { get; set; }
    public int PointsAwarded { get; set; }
    public TransactionType Type { get; set; } = TransactionType.Award;
    public TransactionStatus Status { get; set; } = TransactionStatus.Completed;
    
    // Related Reward
    public Guid RewardId { get; set; }
    public Reward Reward { get; set; } = null!;
    
    // Multi-tenant relationship
    public Guid TenantId { get; set; }
    public Tenant? Tenant { get; set; }
    
    // User who gave the reward
    public string GivenById { get; set; } = default!;
    public ApplicationUser? GivenBy { get; set; }
    
    // User who received the reward
    public string ReceivedById { get; set; } = default!;
    public ApplicationUser? ReceivedBy { get; set; }
}

public enum TransactionType
{
    Award,      // Giving a reward to someone
    Redeem,     // Redeeming points for a reward
    Adjustment  // Administrative adjustment
}

public enum TransactionStatus
{
    Pending,
    Completed,
    Cancelled,
    Rejected
}
