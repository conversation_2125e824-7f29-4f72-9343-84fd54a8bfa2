namespace Cherish.Core.Entities;

public class Attachment : BaseEntity
{
    public string FileName { get; set; } = null!;
    public string OriginalFileName { get; set; } = null!;
    public string ContentType { get; set; } = null!;
    public long FileSize { get; set; }
    public string FilePath { get; set; } = null!;
    public AttachmentType Type { get; set; }

    // Multi-tenant relationship
    public Guid TenantId { get; set; }
    public Tenant? Tenant { get; set; }

    // User who uploaded the attachment
    public string UploadedById { get; set; } = default!;
    public ApplicationUser? UploadedBy { get; set; }

    // Related entity - only one of these will be set
    public Guid? LeadId { get; set; }
    public Lead? Lead { get; set; }

    public Guid? ContactId { get; set; }
    public Contact? Contact { get; set; }
}

public enum AttachmentType
{
    Document,
    Image,
    Spreadsheet,
    Presentation,
    PDF,
    Other
}
