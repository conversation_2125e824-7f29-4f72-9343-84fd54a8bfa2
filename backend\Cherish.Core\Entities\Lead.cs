namespace Cherish.Core.Entities;

public class Lead : BaseEntity
{
    public string FirstName { get; set; } = null!;
    public string LastName { get; set; } = null!;
    public string Email { get; set; } = null!;
    public string? PhoneNumber { get; set; }
    public string? Company { get; set; }
    public string? JobTitle { get; set; }
    public string? Source { get; set; }
    public LeadStatus Status { get; set; } = LeadStatus.New;
    public int? Score { get; set; }
    public string? Description { get; set; }

    // Multi-tenant relationship
    public Guid TenantId { get; set; }
    public Tenant? Tenant { get; set; }

    // Assigned user (optional)
    public string? AssignedToId { get; set; }
    public ApplicationUser? AssignedTo { get; set; }

    // Navigation properties
    public ICollection<LeadActivity> Activities { get; set; } = new List<LeadActivity>();
    public ICollection<LeadNote> Notes { get; set; } = new List<LeadNote>();
    public ICollection<Attachment> Attachments { get; set; } = new List<Attachment>();
    public ICollection<Contact> Contacts { get; set; } = new List<Contact>();

    // Helper properties
    public string FullName => $"{FirstName} {LastName}";
}

public enum LeadStatus
{
    New,
    Contacted,
    Qualified,
    Proposal,
    Negotiation,
    Won,
    Lost
}
