namespace Cherish.Core.Models.Users;

public class UserDto
{
    public string Id { get; set; } = default!;
    public string FirstName { get; set; } = null!;
    public string LastName { get; set; } = null!;
    public string Email { get; set; } = null!;
    public string FullName => $"{FirstName} {LastName}";
    public string? ProfilePictureUrl { get; set; }
    public bool IsActive { get; set; }
    public Guid? TenantId { get; set; }
    public string? TenantName { get; set; }
    public List<string> Roles { get; set; } = new List<string>();
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}
