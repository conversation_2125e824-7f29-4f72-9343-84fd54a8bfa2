namespace Cherish.Core.Entities;

public class Contact : BaseEntity
{
    public string FirstName { get; set; } = null!;
    public string LastName { get; set; } = null!;
    public string Email { get; set; } = null!;
    public string? PhoneNumber { get; set; }
    public string? Company { get; set; }
    public string? JobTitle { get; set; }
    public string? Address { get; set; }
    public string? City { get; set; }
    public string? State { get; set; }
    public string? PostalCode { get; set; }
    public string? Country { get; set; }
    public string? LinkedInUrl { get; set; }
    public string? TwitterUrl { get; set; }
    public string? Description { get; set; }

    // Multi-tenant relationship
    public Guid TenantId { get; set; }
    public Tenant? Tenant { get; set; }

    // Related Lead (optional)
    public Guid? LeadId { get; set; }
    public Lead? Lead { get; set; }

    // Assigned user (optional)
    public string? AssignedToId { get; set; }
    public ApplicationUser? AssignedTo { get; set; }

    // Navigation properties
    public ICollection<ContactActivity> Activities { get; set; } = new List<ContactActivity>();
    public ICollection<ContactNote> Notes { get; set; } = new List<ContactNote>();

    // Helper properties
    public string FullName => $"{FirstName} {LastName}";
}
