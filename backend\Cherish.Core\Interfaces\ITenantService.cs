using Cherish.Core.Models.Tenants;

namespace Cherish.Core.Interfaces;

public interface ITenantService
{
    Task<TenantDto> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<TenantDto?> GetByNameAsync(string name, CancellationToken cancellationToken = default);
    Task<IEnumerable<TenantDto>> GetAllAsync(CancellationToken cancellationToken = default);
    Task<TenantDto> CreateAsync(CreateTenantRequest request, CancellationToken cancellationToken = default);
    Task<TenantDto> UpdateAsync(Guid id, UpdateTenantRequest request, CancellationToken cancellationToken = default);
    Task<bool> DeactivateAsync(Guid id, CancellationToken cancellationToken = default);
    Task<bool> ActivateAsync(Guid id, CancellationToken cancellationToken = default);
    Task<bool> DeleteAsync(Guid id, CancellationToken cancellationToken = default);
}
