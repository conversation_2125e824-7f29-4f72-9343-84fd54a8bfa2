using Cherish.Core.Entities;

namespace Cherish.Core.Models.Leads;

public class LeadDto
{
    public Guid Id { get; set; }
    public string FirstName { get; set; } = null!;
    public string LastName { get; set; } = null!;
    public string Email { get; set; } = null!;
    public string? PhoneNumber { get; set; }
    public string? Company { get; set; }
    public string? JobTitle { get; set; }
    public string? Source { get; set; }
    public LeadStatus Status { get; set; }
    public int? Score { get; set; }
    public string? Notes { get; set; }
    public Guid TenantId { get; set; }
    public string? AssignedToId { get; set; }
    public string? AssignedToName { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }

    // Helper properties
    public string FullName => $"{FirstName} {LastName}";
}
