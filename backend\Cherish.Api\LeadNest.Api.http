@Cherish.Api_HostAddress = http://localhost:9014
@contentType = application/json
@authToken = eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

### Test Public Endpoint
GET {{Cherish.Api_HostAddress}}/api/test/public
Accept: {{contentType}}

### Test Authenticated Endpoint
GET {{Cherish.Api_HostAddress}}/api/test/authenticated
Accept: {{contentType}}
Authorization: Bearer {{authToken}}

### Test Admin Endpoint
GET {{Cherish.Api_HostAddress}}/api/test/admin
Accept: {{contentType}}
Authorization: Bearer {{authToken}}

### Register a new user
POST {{Cherish.Api_HostAddress}}/api/auth/register
Content-Type: {{contentType}}

{
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "password": "Password123!",
  "confirmPassword": "Password123!"
}

### Login
POST {{Cherish.Api_HostAddress}}/api/auth/login
Content-Type: {{contentType}}

{
  "email": "<EMAIL>",
  "password": "Password123!"
}

### Google Login
POST {{Cherish.Api_HostAddress}}/api/auth/google-login
Content-Type: {{contentType}}

{
  "token": "google-id-token-here"
}

### Refresh Token
POST {{Cherish.Api_HostAddress}}/api/auth/refresh-token
Content-Type: {{contentType}}

{
  "refreshToken": "your-refresh-token-here"
}

### Logout
POST {{Cherish.Api_HostAddress}}/api/auth/logout
Content-Type: {{contentType}}
Authorization: Bearer {{authToken}}

### Get All Users (Admin/SuperAdmin)
GET {{Cherish.Api_HostAddress}}/api/users
Accept: {{contentType}}
Authorization: Bearer {{authToken}}

### Get User by ID
GET {{Cherish.Api_HostAddress}}/api/users/00000000-0000-0000-0000-000000000000
Accept: {{contentType}}
Authorization: Bearer {{authToken}}

### Get Current User
GET {{Cherish.Api_HostAddress}}/api/users/me
Accept: {{contentType}}
Authorization: Bearer {{authToken}}

### Create User (Admin/SuperAdmin)
POST {{Cherish.Api_HostAddress}}/api/users
Content-Type: {{contentType}}
Authorization: Bearer {{authToken}}

{
  "firstName": "Jane",
  "lastName": "Smith",
  "email": "<EMAIL>",
  "password": "Password123!",
  "roleIds": ["00000000-0000-0000-0000-000000000000"],
  "tenantId": "00000000-0000-0000-0000-000000000000"
}

### Update User
PUT {{Cherish.Api_HostAddress}}/api/users/00000000-0000-0000-0000-000000000000
Content-Type: {{contentType}}
Authorization: Bearer {{authToken}}

{
  "firstName": "Jane",
  "lastName": "Smith-Updated",
  "email": "<EMAIL>",
  "isActive": true
}

### Get All Roles (Admin/SuperAdmin)
GET {{Cherish.Api_HostAddress}}/api/roles
Accept: {{contentType}}
Authorization: Bearer {{authToken}}

### Get Role by ID
GET {{Cherish.Api_HostAddress}}/api/roles/00000000-0000-0000-0000-000000000000
Accept: {{contentType}}
Authorization: Bearer {{authToken}}

### Get Roles by Tenant ID
GET {{Cherish.Api_HostAddress}}/api/roles/tenant/00000000-0000-0000-0000-000000000000
Accept: {{contentType}}
Authorization: Bearer {{authToken}}

### Create Role (Admin/SuperAdmin)
POST {{Cherish.Api_HostAddress}}/api/roles
Content-Type: {{contentType}}
Authorization: Bearer {{authToken}}

{
  "name": "SalesManager",
  "description": "Sales manager role with lead management permissions",
  "tenantId": "00000000-0000-0000-0000-000000000000",
  "permissionIds": [
    "00000000-0000-0000-0000-000000000000",
    "00000000-0000-0000-0000-000000000001"
  ]
}

### Update Role
PUT {{Cherish.Api_HostAddress}}/api/roles/00000000-0000-0000-0000-000000000000
Content-Type: {{contentType}}
Authorization: Bearer {{authToken}}

{
  "name": "SalesManager",
  "description": "Updated description for sales manager role",
  "permissionIds": [
    "00000000-0000-0000-0000-000000000000",
    "00000000-0000-0000-0000-000000000001",
    "00000000-0000-0000-0000-000000000002"
  ]
}

### Get All Permissions (Admin/SuperAdmin)
GET {{Cherish.Api_HostAddress}}/api/permissions
Accept: {{contentType}}
Authorization: Bearer {{authToken}}

### Get Permission by ID
GET {{Cherish.Api_HostAddress}}/api/permissions/00000000-0000-0000-0000-000000000000
Accept: {{contentType}}
Authorization: Bearer {{authToken}}

### Create Permission (SuperAdmin only)
POST {{Cherish.Api_HostAddress}}/api/permissions
Content-Type: {{contentType}}
Authorization: Bearer {{authToken}}

{
  "name": "ViewDashboard",
  "description": "Permission to view the dashboard",
  "category": "Dashboard"
}

### Get All Tenants (SuperAdmin)
GET {{Cherish.Api_HostAddress}}/api/tenants
Accept: {{contentType}}
Authorization: Bearer {{authToken}}

### Get Tenant by ID (SuperAdmin)
GET {{Cherish.Api_HostAddress}}/api/tenants/00000000-0000-0000-0000-000000000000
Accept: {{contentType}}
Authorization: Bearer {{authToken}}

### Create Tenant (SuperAdmin)
POST {{Cherish.Api_HostAddress}}/api/tenants
Content-Type: {{contentType}}
Authorization: Bearer {{authToken}}

{
  "name": "Acme Corporation",
  "subdomain": "acme",
  "logoUrl": "https://example.com/logo.png",
  "adminFirstName": "Admin",
  "adminLastName": "User",
  "adminEmail": "<EMAIL>",
  "adminPassword": "Password123!"
}

### Update Tenant (SuperAdmin)
PUT {{Cherish.Api_HostAddress}}/api/tenants/00000000-0000-0000-0000-000000000000
Content-Type: {{contentType}}
Authorization: Bearer {{authToken}}

{
  "name": "Acme Corporation Updated",
  "logoUrl": "https://example.com/updated-logo.png",
  "isActive": true
}
