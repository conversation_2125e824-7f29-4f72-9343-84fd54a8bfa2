using Cherish.Core.Interfaces;
using Cherish.Core.Models.Dashboard;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Cherish.Api.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class DashboardController : ControllerBase
{
    private readonly IDashboardService _dashboardService;

    public DashboardController(IDashboardService dashboardService)
    {
        _dashboardService = dashboardService;
    }

    [HttpGet("stats")]
    [ProducesResponseType(typeof(DashboardStatsDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> GetDashboardStats(CancellationToken cancellationToken)
    {
        // Get tenant ID from claims
        var tenantIdClaim = User.FindFirst("TenantId");

        if (tenantIdClaim == null || !Guid.TryParse(tenantIdClaim.Value, out var tenantId))
        {
            return BadRequest("Invalid tenant ID");
        }

        var stats = await _dashboardService.GetDashboardStatsAsync(tenantId, cancellationToken);
        return Ok(stats);
    }

    [HttpGet("leads-by-status")]
    [ProducesResponseType(typeof(IEnumerable<LeadsByStatusDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> GetLeadsByStatus(CancellationToken cancellationToken)
    {
        // Get tenant ID from claims
        var tenantIdClaim = User.FindFirst("TenantId");

        if (tenantIdClaim == null || !Guid.TryParse(tenantIdClaim.Value, out var tenantId))
        {
            return BadRequest("Invalid tenant ID");
        }

        var leadsByStatus = await _dashboardService.GetLeadsByStatusAsync(tenantId, cancellationToken);
        return Ok(leadsByStatus);
    }

    [HttpGet("leads-by-source")]
    [ProducesResponseType(typeof(IEnumerable<LeadsBySourceDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> GetLeadsBySource(CancellationToken cancellationToken)
    {
        // Get tenant ID from claims
        var tenantIdClaim = User.FindFirst("TenantId");

        if (tenantIdClaim == null || !Guid.TryParse(tenantIdClaim.Value, out var tenantId))
        {
            return BadRequest("Invalid tenant ID");
        }

        var leadsBySource = await _dashboardService.GetLeadsBySourceAsync(tenantId, cancellationToken);
        return Ok(leadsBySource);
    }

    [HttpGet("recent-leads")]
    [ProducesResponseType(typeof(IEnumerable<RecentLeadDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> GetRecentLeads([FromQuery] int count = 5, CancellationToken cancellationToken = default)
    {
        // Get tenant ID from claims
        var tenantIdClaim = User.FindFirst("TenantId");

        if (tenantIdClaim == null || !Guid.TryParse(tenantIdClaim.Value, out var tenantId))
        {
            return BadRequest("Invalid tenant ID");
        }

        var recentLeads = await _dashboardService.GetRecentLeadsAsync(tenantId, count, cancellationToken);
        return Ok(recentLeads);
    }
}
